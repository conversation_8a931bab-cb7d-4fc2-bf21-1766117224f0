// Report Settings Controller - HTTP endpoints for report settings operations
import { FastifyRequest, FastifyReply } from 'fastify';
import { ReportSettingsService } from '../../application/services/ReportSettings.Service.js';
import { 
  CreateReportSettingsDto, 
  UpdateReportSettingsDto,
  CreateReportSettingsSchema,
  UpdateReportSettingsSchema
} from '../../application/dto/reportSettings.dto.js';
import { ValidationError } from '../../application/validation/DtoValidator.js';

export class ReportSettingsController {
  private reportSettingsService: ReportSettingsService;

  constructor(prisma: any) {
    this.reportSettingsService = new ReportSettingsService(prisma);
  }

  async getAllReportSettings(request: FastifyRequest, reply: FastifyReply) {
    try {
      console.log('📊 Getting all report settings...');
      const reportSettings = await this.reportSettingsService.getAllReportSettingsAsync();
      console.log('✅ Found report settings:', reportSettings.length);
      return reply.status(200).send(reportSettings);
    } catch (error) {
      console.error('💥 Get all report settings error:', error);
      return reply.status(500).send({ error: 'Internal server error' });
    }
  }

  async getAllActiveReportSettings(request: FastifyRequest, reply: FastifyReply) {
    try {
      console.log('📊 Getting all active report settings...');
      const reportSettings = await this.reportSettingsService.getAllActiveReportSettingsAsync();
      console.log('✅ Found active report settings:', reportSettings.length);
      return reply.status(200).send(reportSettings);
    } catch (error) {
      console.error('💥 Get all active report settings error:', error);
      return reply.status(500).send({ error: 'Internal server error' });
    }
  }

  async getReportSettingById(request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) {
    try {
      const { id } = request.params;
      console.log('📊 Getting report setting by ID:', id);
      
      const reportSetting = await this.reportSettingsService.getReportSettingByIdAsync(id);
      console.log('✅ Found report setting:', reportSetting.reportType);
      
      return reply.status(200).send(reportSetting);
    } catch (error) {
      console.error('💥 Get report setting by ID error:', error);
      const message = error instanceof Error ? error.message : 'An error occurred';
      
      if (message.toLowerCase().includes('not found')) {
        return reply.status(404).send({ error: message });
      }
      
      return reply.status(500).send({ error: 'Internal server error' });
    }
  }

  async getReportSettingByType(request: FastifyRequest<{ Params: { type: string } }>, reply: FastifyReply) {
    try {
      const { type } = request.params;
      console.log('📊 Getting report setting by type:', type);
      
      const reportSetting = await this.reportSettingsService.getReportSettingByTypeAsync(type);
      
      if (!reportSetting) {
        return reply.status(404).send({ error: 'Report setting not found' });
      }
      
      console.log('✅ Found report setting:', reportSetting.reportType);
      return reply.status(200).send(reportSetting);
    } catch (error) {
      console.error('💥 Get report setting by type error:', error);
      return reply.status(500).send({ error: 'Internal server error' });
    }
  }

  async createReportSetting(request: FastifyRequest<{ Body: CreateReportSettingsDto }>, reply: FastifyReply) {
    try {
      const createDto = request.body;
      const userId = (request.user && request.user.userId) || '';
      
      console.log('📊 Creating report setting:', createDto.reportType);
      
      const createdReportSetting = await this.reportSettingsService.createReportSettingAsync(createDto, userId);
      console.log('✅ Created report setting:', createdReportSetting.reportSettingsId);
      
      return reply.status(201).send({
        success: true,
        message: 'Report setting created successfully',
        data: createdReportSetting
      });
    } catch (error) {
      console.error('💥 Create report setting error:', error);
      const message = error instanceof Error ? error.message : 'An error occurred';
      
      if (error instanceof ValidationError) {
        return reply.status(400).send({
          success: false,
          message: 'Validation failed',
          errors: error.errors
        });
      }
      
      if (message.toLowerCase().includes('already exists')) {
        return reply.status(409).send({
          success: false,
          message: message
        });
      }
      
      return reply.status(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  async updateReportSetting(request: FastifyRequest<{ Params: { id: string }, Body: UpdateReportSettingsDto }>, reply: FastifyReply) {
    try {
      const { id } = request.params;
      const updateDto = request.body;
      const userId = (request.user && request.user.userId) || '';
      
      console.log('📊 Updating report setting:', id);
      
      const updatedReportSetting = await this.reportSettingsService.updateReportSettingAsync(id, updateDto, userId);
      console.log('✅ Updated report setting:', updatedReportSetting.reportSettingsId);
      
      return reply.status(200).send({
        success: true,
        message: 'Report setting updated successfully',
        data: updatedReportSetting
      });
    } catch (error) {
      console.error('💥 Update report setting error:', error);
      const message = error instanceof Error ? error.message : 'An error occurred';
      
      if (error instanceof ValidationError) {
        return reply.status(400).send({
          success: false,
          message: 'Validation failed',
          errors: error.errors
        });
      }
      
      if (message.toLowerCase().includes('not found')) {
        return reply.status(404).send({
          success: false,
          message: message
        });
      }
      
      if (message.toLowerCase().includes('already exists')) {
        return reply.status(409).send({
          success: false,
          message: message
        });
      }
      
      return reply.status(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  async deleteReportSetting(request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) {
    try {
      const { id } = request.params;
      console.log('📊 Deleting report setting:', id);
      
      await this.reportSettingsService.deleteReportSettingAsync(id);
      console.log('✅ Deleted report setting:', id);
      
      return reply.status(200).send({
        success: true,
        message: 'Report setting deleted successfully'
      });
    } catch (error) {
      console.error('💥 Delete report setting error:', error);
      const message = error instanceof Error ? error.message : 'An error occurred';
      
      if (message.toLowerCase().includes('not found')) {
        return reply.status(404).send({
          success: false,
          message: message
        });
      }
      
      return reply.status(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  async softDeleteReportSetting(request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) {
    try {
      const { id } = request.params;
      console.log('📊 Soft deleting report setting:', id);
      
      await this.reportSettingsService.softDeleteReportSettingAsync(id);
      console.log('✅ Soft deleted report setting:', id);
      
      return reply.status(200).send({
        success: true,
        message: 'Report setting deactivated successfully'
      });
    } catch (error) {
      console.error('💥 Soft delete report setting error:', error);
      const message = error instanceof Error ? error.message : 'An error occurred';
      
      if (message.toLowerCase().includes('not found')) {
        return reply.status(404).send({
          success: false,
          message: message
        });
      }
      
      return reply.status(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  async getCurrentlyOpenReportSettings(request: FastifyRequest, reply: FastifyReply) {
    try {
      console.log('📊 Getting currently open report settings...');
      const reportSettings = await this.reportSettingsService.getCurrentlyOpenReportSettingsAsync();
      console.log('✅ Found currently open report settings:', reportSettings.length);
      return reply.status(200).send(reportSettings);
    } catch (error) {
      console.error('💥 Get currently open report settings error:', error);
      return reply.status(500).send({ error: 'Internal server error' });
    }
  }

  async getUpcomingReportSettings(request: FastifyRequest<{ Querystring: { days?: string } }>, reply: FastifyReply) {
    try {
      const days = request.query.days ? parseInt(request.query.days, 10) : 7;
      console.log('📊 Getting upcoming report settings for next', days, 'days...');
      
      const reportSettings = await this.reportSettingsService.getUpcomingReportSettingsAsync(days);
      console.log('✅ Found upcoming report settings:', reportSettings.length);
      
      return reply.status(200).send(reportSettings);
    } catch (error) {
      console.error('💥 Get upcoming report settings error:', error);
      return reply.status(500).send({ error: 'Internal server error' });
    }
  }
}
