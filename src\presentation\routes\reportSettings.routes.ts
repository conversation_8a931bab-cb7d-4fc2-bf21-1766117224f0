// Report Settings routes
import { FastifyInstance } from 'fastify';
import { ReportSettingsController } from '../controllers/ReportSettings.Controller.js';
import {
  CreateReportSettingsDto,
  UpdateReportSettingsDto,
  CreateReportSettingsDtoSchema,
  UpdateReportSettingsDtoSchema
} from '../../application/dto/reportSettings.dto.js';
import { verifyJWT } from '../middleware/auth.js';

export default async function reportSettingsRoutes(fastify: FastifyInstance) {
  const reportSettingsController = new ReportSettingsController(fastify.prisma);

  // Swagger schemas for documentation
  const reportSettingsResponseSchema = {
    type: 'object',
    properties: {
      success: { type: 'boolean' },
      message: { type: 'string' },
      data: {
        type: 'object',
        properties: {
          reportSettingsId: { type: 'string' },
          reportType: { type: 'string' },
          reportOpeningDate: { type: 'string', format: 'date-time' },
          reportClosingDate: { type: 'string', format: 'date-time' },
          isActive: { type: 'boolean' },
          createdBy: { type: 'string' },
          createdAt: { type: 'string', format: 'date-time' },
          updatedAt: { type: 'string', format: 'date-time' }
        }
      }
    }
  };

  const errorResponseSchema = {
    type: 'object',
    properties: {
      success: { type: 'boolean' },
      message: { type: 'string' },
      errors: {
        type: 'array',
        items: { type: 'string' }
      }
    }
  };

  const reportSettingsArraySchema = {
    type: 'array',
    items: {
      type: 'object',
      properties: {
        reportSettingsId: { type: 'string' },
        reportType: { type: 'string' },
        reportOpeningDate: { type: 'string', format: 'date-time' },
        reportClosingDate: { type: 'string', format: 'date-time' },
        isActive: { type: 'boolean' },
        createdBy: { type: 'string' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' }
      }
    }
  };

  // Get all report settings
  fastify.get('/get-all-report-settings', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Get all report settings',
      tags: ['Report Settings'],
      security: [{ bearerAuth: [] }],
      response: {
        200: reportSettingsArraySchema,
        500: errorResponseSchema
      }
    }
  }, reportSettingsController.getAllReportSettings.bind(reportSettingsController));

  // Get all active report settings
  fastify.get('/get-all-active-report-settings', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Get all active report settings',
      tags: ['Report Settings'],
      security: [{ bearerAuth: [] }],
      response: {
        200: reportSettingsArraySchema,
        500: errorResponseSchema
      }
    }
  }, reportSettingsController.getAllActiveReportSettings.bind(reportSettingsController));

  // Get report setting by ID
  fastify.get<{ Params: { id: string } }>('/get-report-setting-by-id/:id', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Get report setting by ID',
      tags: ['Report Settings'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      response: {
        200: reportSettingsResponseSchema.properties.data,
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, reportSettingsController.getReportSettingById.bind(reportSettingsController));

  // Get report setting by type
  fastify.get<{ Params: { type: string } }>('/get-report-setting-by-type/:type', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Get report setting by type',
      tags: ['Report Settings'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          type: { type: 'string' }
        },
        required: ['type']
      },
      response: {
        200: reportSettingsResponseSchema.properties.data,
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, reportSettingsController.getReportSettingByType.bind(reportSettingsController));

  // Create report setting
  fastify.post<{ Body: CreateReportSettingsDto }>('/add-report-settings', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Create a new report setting',
      tags: ['Report Settings'],
      security: [{ bearerAuth: [] }],
      body: {
        type: 'object',
        required: ['reportType', 'reportOpeningDate', 'reportClosingDate'],
        properties: {
          reportType: { type: 'string', minLength: 1, maxLength: 100 },
          reportOpeningDate: { type: 'string', format: 'date-time' },
          reportClosingDate: { type: 'string', format: 'date-time' }
        }
      },
      response: {
        201: reportSettingsResponseSchema,
        400: errorResponseSchema,
        409: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, reportSettingsController.createReportSetting.bind(reportSettingsController));

  // Update report setting
  fastify.put<{ Params: { id: string }, Body: UpdateReportSettingsDto }>('/update-report-setting/:id', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Update an existing report setting',
      tags: ['Report Settings'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      body: {
        type: 'object',
        properties: {
          reportType: { type: 'string', minLength: 1, maxLength: 100 },
          reportOpeningDate: { type: 'string', format: 'date-time' },
          reportClosingDate: { type: 'string', format: 'date-time' },
          isActive: { type: 'boolean' }
        }
      },
      response: {
        200: reportSettingsResponseSchema,
        400: errorResponseSchema,
        404: errorResponseSchema,
        409: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, reportSettingsController.updateReportSetting.bind(reportSettingsController));

  // Delete report setting
  fastify.delete<{ Params: { id: string } }>('/delete-report-setting/:id', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Delete a report setting',
      tags: ['Report Settings'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        },
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, reportSettingsController.deleteReportSetting.bind(reportSettingsController));

  // Soft delete report setting (deactivate)
  fastify.patch<{ Params: { id: string } }>('/deactivate-report-setting/:id', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Deactivate a report setting (soft delete)',
      tags: ['Report Settings'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        },
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, reportSettingsController.softDeleteReportSetting.bind(reportSettingsController));

  // Get currently open report settings
  fastify.get('/get-currently-open-report-settings', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Get currently open report settings',
      tags: ['Report Settings'],
      security: [{ bearerAuth: [] }],
      response: {
        200: reportSettingsArraySchema,
        500: errorResponseSchema
      }
    }
  }, reportSettingsController.getCurrentlyOpenReportSettings.bind(reportSettingsController));

  // Get upcoming report settings
  fastify.get<{ Querystring: { days?: string } }>('/get-upcoming-report-settings', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Get upcoming report settings',
      tags: ['Report Settings'],
      security: [{ bearerAuth: [] }],
      querystring: {
        type: 'object',
        properties: {
          days: { type: 'string', pattern: '^[1-9]\\d*$' }
        }
      },
      response: {
        200: reportSettingsArraySchema,
        500: errorResponseSchema
      }
    }
  }, reportSettingsController.getUpcomingReportSettings.bind(reportSettingsController));
}
