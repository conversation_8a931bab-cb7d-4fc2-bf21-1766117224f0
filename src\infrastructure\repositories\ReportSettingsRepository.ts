// Report Settings Repository - Data access layer for report settings
import { PrismaClient } from '../../generated/prisma/index.js';
import { ReportSettings } from '../../domain/ReportSettings.entity.js';
import { ReportSettingsMapper } from '../../helpers/mappers.js';

/**
 * Report Settings Repository Implementation
 * Handles data access operations for report settings
 */
export class ReportSettingsRepository {
  constructor(private prisma: PrismaClient) {}

  /**
   * Gets all report settings
   */
  async getAllReportSettingsAsync(): Promise<ReportSettings[]> {
    const prismaReportSettings = await this.prisma.reportSettings.findMany({
      orderBy: { CreatedAt: 'desc' }
    });
    return ReportSettingsMapper.toDomainArray(prismaReportSettings);
  }

  /**
   * Gets all active report settings
   */
  async getAllActiveReportSettingsAsync(): Promise<ReportSettings[]> {
    const prismaReportSettings = await this.prisma.reportSettings.findMany({
      where: { IsActive: true },
      orderBy: { CreatedAt: 'desc' }
    });
    return ReportSettingsMapper.toDomainArray(prismaReportSettings);
  }

  /**
   * Gets a report setting by ID
   * @param id - Report Settings ID
   */
  async getReportSettingByIdAsync(id: string): Promise<ReportSettings | null> {
    const prismaReportSetting = await this.prisma.reportSettings.findUnique({
      where: { ReportSettingsId: id }
    });

    return prismaReportSetting ? ReportSettingsMapper.toDomain(prismaReportSetting) : null;
  }

  /**
   * Gets a report setting by report type
   * @param reportType - Report type
   */
  async getReportSettingByTypeAsync(reportType: string): Promise<ReportSettings | null> {
    const prismaReportSetting = await this.prisma.reportSettings.findFirst({
      where: { 
        ReportType: reportType,
        IsActive: true 
      }
    });

    return prismaReportSetting ? ReportSettingsMapper.toDomain(prismaReportSetting) : null;
  }

  /**
   * Adds a new report setting
   * @param reportSetting - Report setting to add
   */
  async addReportSettingAsync(reportSetting: ReportSettings): Promise<ReportSettings> {
    const prismaData = ReportSettingsMapper.toPrisma(reportSetting);
    const createdReportSetting = await this.prisma.reportSettings.create({
      data: prismaData
    });

    return ReportSettingsMapper.toDomain(createdReportSetting);
  }

  /**
   * Updates an existing report setting
   * @param reportSetting - Report setting to update
   */
  async updateReportSettingAsync(reportSetting: ReportSettings): Promise<ReportSettings> {
    const prismaData = ReportSettingsMapper.toPrisma(reportSetting);
    const updatedReportSetting = await this.prisma.reportSettings.update({
      where: { ReportSettingsId: reportSetting.reportSettingsId },
      data: prismaData
    });

    return ReportSettingsMapper.toDomain(updatedReportSetting);
  }

  /**
   * Deletes a report setting
   * @param id - Report Settings ID
   */
  async deleteReportSettingAsync(id: string): Promise<void> {
    await this.prisma.reportSettings.delete({
      where: { ReportSettingsId: id }
    });
  }

  /**
   * Soft deletes a report setting (sets IsActive to false)
   * @param id - Report Settings ID
   */
  async softDeleteReportSettingAsync(id: string): Promise<void> {
    await this.prisma.reportSettings.update({
      where: { ReportSettingsId: id },
      data: { IsActive: false, UpdatedAt: new Date() }
    });
  }

  /**
   * Checks if a report setting exists
   * @param id - Report Settings ID
   */
  async existsAsync(id: string): Promise<boolean> {
    const count = await this.prisma.reportSettings.count({
      where: { ReportSettingsId: id }
    });
    return count > 0;
  }

  /**
   * Checks if a report type already exists
   * @param reportType - Report type to check
   * @param excludeId - Optional ID to exclude from check (for updates)
   */
  async reportTypeExistsAsync(reportType: string, excludeId?: string): Promise<boolean> {
    const whereClause: any = { ReportType: reportType };
    if (excludeId) {
      whereClause.ReportSettingsId = { not: excludeId };
    }

    const count = await this.prisma.reportSettings.count({
      where: whereClause
    });
    return count > 0;
  }

  /**
   * Gets currently open report settings
   */
  async getCurrentlyOpenReportSettingsAsync(): Promise<ReportSettings[]> {
    const now = new Date();
    const prismaReportSettings = await this.prisma.reportSettings.findMany({
      where: {
        IsActive: true,
        ReportOpeningDate: { lte: now },
        ReportClosingDate: { gte: now }
      },
      orderBy: { CreatedAt: 'desc' }
    });
    return ReportSettingsMapper.toDomainArray(prismaReportSettings);
  }

  /**
   * Gets report settings that will open soon
   * @param daysAhead - Number of days ahead to check
   */
  async getUpcomingReportSettingsAsync(daysAhead: number = 7): Promise<ReportSettings[]> {
    const now = new Date();
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + daysAhead);

    const prismaReportSettings = await this.prisma.reportSettings.findMany({
      where: {
        IsActive: true,
        ReportOpeningDate: { 
          gte: now,
          lte: futureDate 
        }
      },
      orderBy: { ReportOpeningDate: 'asc' }
    });
    return ReportSettingsMapper.toDomainArray(prismaReportSettings);
  }
}
