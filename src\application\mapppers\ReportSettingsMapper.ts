// Mapper to convert between Prisma models and Domain entities
import { ReportSettings as PrismaReportSettings } from '../../generated/prisma/index.js';
import { ReportSettings } from '../../domain/ReportSettings.entity.js';

export class ReportSettingsMapper {
  // Convert from Prisma model to Domain entity
  static toDomain(prismaReportSettings: PrismaReportSettings): ReportSettings {
    return new ReportSettings(
      prismaReportSettings.ReportSettingsId,
      prismaReportSettings.ReportType,
      prismaReportSettings.ReportOpeningDate,
      prismaReportSettings.ReportClosingDate,
      prismaReportSettings.IsActive,
      prismaReportSettings.CreatedBy,
      prismaReportSettings.CreatedAt,
      prismaReportSettings.UpdatedAt
    );
  }

  // Convert from Domain entity to Prisma format
  static toPrisma(reportSettings: ReportSettings): Omit<PrismaReportSettings, 'Creator'> {
    return {
      ReportSettingsId: reportSettings.reportSettingsId,
      ReportType: reportSettings.reportType,
      ReportOpeningDate: reportSettings.reportOpeningDate,
      ReportClosingDate: reportSettings.reportClosingDate,
      IsActive: reportSettings.isActive,
      CreatedBy: reportSettings.createdBy,
      CreatedAt: reportSettings.createdAt,
      UpdatedAt: reportSettings.updatedAt
    };
  }

  // Convert array of Prisma models to Domain entities
  static toDomainArray(prismaReportSettings: PrismaReportSettings[]): ReportSettings[] {
    return prismaReportSettings.map(this.toDomain);
  }

  // Convert array of Domain entities to Prisma format
  static toPrismaArray(reportSettings: ReportSettings[]): Omit<PrismaReportSettings, 'Creator'>[] {
    return reportSettings.map(this.toPrisma);
  }
}
