// Domain Entity for Report Settings
export class ReportSettings {
  constructor(
    public readonly reportSettingsId: string,
    public readonly reportType: string,
    public readonly reportOpeningDate: Date,
    public readonly reportClosingDate: Date,
    public readonly isActive: boolean = true,
    public readonly createdBy: string,
    public readonly createdAt: Date = new Date(),
    public readonly updatedAt: Date = new Date()
  ) {}

  // Domain methods
  public deactivate(): ReportSettings {
    return new ReportSettings(
      this.reportSettingsId,
      this.reportType,
      this.reportOpeningDate,
      this.reportClosingDate,
      false,
      this.createdBy,
      this.createdAt,
      new Date()
    );
  }

  public activate(): ReportSettings {
    return new ReportSettings(
      this.reportSettingsId,
      this.reportType,
      this.reportOpeningDate,
      this.reportClosingDate,
      true,
      this.createdBy,
      this.createdAt,
      new Date()
    );
  }

  public updateDates(newOpeningDate: Date, newClosingDate: Date): ReportSettings {
    if (newClosingDate < newOpeningDate) {
      throw new Error('Closing date must be on or after opening date');
    }

    return new ReportSettings(
      this.reportSettingsId,
      this.reportType,
      newOpeningDate,
      newClosingDate,
      this.isActive,
      this.createdBy,
      this.createdAt,
      new Date()
    );
  }

  public updateReportType(newReportType: string): ReportSettings {
    if (!newReportType || newReportType.trim().length === 0) {
      throw new Error('Report type cannot be empty');
    }

    return new ReportSettings(
      this.reportSettingsId,
      newReportType.trim(),
      this.reportOpeningDate,
      this.reportClosingDate,
      this.isActive,
      this.createdBy,
      this.createdAt,
      new Date()
    );
  }

  // Business rules
  public canBeDeleted(): boolean {
    return !this.isActive;
  }

  public isCurrentlyOpen(): boolean {
    const now = new Date();
    return this.isActive && 
           now >= this.reportOpeningDate && 
           now <= this.reportClosingDate;
  }

  public isValidDateRange(): boolean {
    return this.reportClosingDate >= this.reportOpeningDate;
  }

  public isValidForSubmission(): boolean {
    return this.isActive && 
           this.isValidDateRange() && 
           this.reportType.trim().length > 0;
  }

  public getDurationInDays(): number {
    const timeDiff = this.reportClosingDate.getTime() - this.reportOpeningDate.getTime();
    return Math.ceil(timeDiff / (1000 * 3600 * 24));
  }

  public getTimeUntilOpening(): number | null {
    const now = new Date();
    if (now >= this.reportOpeningDate) {
      return null; // Already open or past
    }
    return this.reportOpeningDate.getTime() - now.getTime();
  }

  public getTimeUntilClosing(): number | null {
    const now = new Date();
    if (now >= this.reportClosingDate) {
      return null; // Already closed
    }
    return this.reportClosingDate.getTime() - now.getTime();
  }
}
