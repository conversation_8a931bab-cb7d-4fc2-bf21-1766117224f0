// Report Settings DTOs - Using Zod for validation
import { z } from "zod";

// Base Report Settings DTO Schema for responses
export const ReportSettingsSchema = z.object({
  reportSettingsId: z.uuid(),
  reportType: z.string().min(1, "Report type is required").max(100, "Report type too long"),
  reportOpeningDate: z.date(),
  reportClosingDate: z.date(),
  isActive: z.boolean(),
  createdBy: z.uuid(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type ReportSettingsDto = z.infer<typeof ReportSettingsSchema>;

// Create Report Settings DTO Schema
export const CreateReportSettingsSchema = z.object({
  reportType: z.string().min(1, "Report type is required").max(100, "Report type too long"),
  reportOpeningDate: z.string().datetime("Invalid opening date format"),
  reportClosingDate: z.string().datetime("Invalid closing date format"),
}).refine((data) => {
  const openingDate = new Date(data.reportOpeningDate);
  const closingDate = new Date(data.reportClosingDate);
  return closingDate >= openingDate;
}, {
  message: "Closing date must be on or after opening date",
  path: ["reportClosingDate"],
});

export type CreateReportSettingsDto = z.infer<typeof CreateReportSettingsSchema>;

// Update Report Settings DTO Schema
export const UpdateReportSettingsSchema = z.object({
  reportType: z.string().min(1, "Report type is required").max(100, "Report type too long").optional(),
  reportOpeningDate: z.string().datetime("Invalid opening date format").optional(),
  reportClosingDate: z.string().datetime("Invalid closing date format").optional(),
  isActive: z.boolean().optional(),
}).refine((data) => {
  if (data.reportOpeningDate && data.reportClosingDate) {
    const openingDate = new Date(data.reportOpeningDate);
    const closingDate = new Date(data.reportClosingDate);
    return closingDate >= openingDate;
  }
  return true;
}, {
  message: "Closing date must be on or after opening date",
  path: ["reportClosingDate"],
});

export type UpdateReportSettingsDto = z.infer<typeof UpdateReportSettingsSchema>;

// Report Settings Response DTO Schema (for API responses)
export const ReportSettingsResponseSchema = z.object({
  reportSettingsId: z.uuid(),
  reportType: z.string(),
  reportOpeningDate: z.date(),
  reportClosingDate: z.date(),
  isActive: z.boolean(),
  createdBy: z.uuid(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type ReportSettingsResponseDto = z.infer<typeof ReportSettingsResponseSchema>;

// Validation schemas for Fastify
export const CreateReportSettingsDtoSchema = CreateReportSettingsSchema;
export const UpdateReportSettingsDtoSchema = UpdateReportSettingsSchema;
