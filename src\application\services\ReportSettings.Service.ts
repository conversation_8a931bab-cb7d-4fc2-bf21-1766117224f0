// Report Settings Service - Business logic for report settings operations
import { PrismaClient } from '../../generated/prisma/index.js';
import { ReportSettings } from '../../domain/ReportSettings.entity.js';
import { ReportSettingsRepository } from '../../infrastructure/repositories/ReportSettingsRepository.js';
import {
  ReportSettingsDto,
  CreateReportSettingsDto,
  UpdateReportSettingsDto,
  CreateReportSettingsSchema,
  UpdateReportSettingsSchema,
  ReportSettingsResponseDto
} from '../dto/reportSettings.dto.js';
import { DtoValidator } from '../validation/DtoValidator.js';
import crypto from 'crypto';

// Helper function to convert database entity to DTO
function toReportSettingsDto(dbReportSettings: any): ReportSettingsDto {
  return {
    reportSettingsId: dbReportSettings.ReportSettingsId,
    reportType: dbReportSettings.ReportType,
    reportOpeningDate: dbReportSettings.ReportOpeningDate,
    reportClosingDate: dbReportSettings.ReportClosingDate,
    isActive: dbReportSettings.IsActive,
    createdBy: dbReportSettings.CreatedBy,
    createdAt: dbReportSettings.CreatedAt,
    updatedAt: dbReportSettings.UpdatedAt
  };
}

export class ReportSettingsService {
  private repository: ReportSettingsRepository;

  constructor(private prisma: PrismaClient) {
    this.repository = new ReportSettingsRepository(prisma);
  }

  async getAllReportSettingsAsync(): Promise<ReportSettingsDto[]> {
    const prismaReportSettings = await this.prisma.reportSettings.findMany({
      orderBy: { CreatedAt: 'desc' }
    });

    return prismaReportSettings.map(toReportSettingsDto);
  }

  async getAllActiveReportSettingsAsync(): Promise<ReportSettingsDto[]> {
    const prismaReportSettings = await this.prisma.reportSettings.findMany({
      where: { IsActive: true },
      orderBy: { CreatedAt: 'desc' }
    });

    return prismaReportSettings.map(toReportSettingsDto);
  }

  async getReportSettingByIdAsync(id: string): Promise<ReportSettingsDto> {
    const prismaReportSetting = await this.prisma.reportSettings.findUnique({
      where: { ReportSettingsId: id }
    });

    if (!prismaReportSetting) {
      throw new Error('Report setting not found');
    }

    return toReportSettingsDto(prismaReportSetting);
  }

  async getReportSettingByTypeAsync(reportType: string): Promise<ReportSettingsDto | null> {
    const prismaReportSetting = await this.prisma.reportSettings.findFirst({
      where: { 
        ReportType: reportType,
        IsActive: true 
      }
    });

    return prismaReportSetting ? toReportSettingsDto(prismaReportSetting) : null;
  }

  async createReportSettingAsync(createDto: CreateReportSettingsDto, createdBy: string): Promise<ReportSettingsDto> {
    // Validate DTO
    const validatedDto = DtoValidator.validate(createDto, CreateReportSettingsSchema);

    // Sanitize input
    const sanitizedDto = DtoValidator.sanitizeUserInput(validatedDto);

    // Check if report type already exists
    const existingReportSetting = await this.prisma.reportSettings.findFirst({
      where: { ReportType: sanitizedDto.reportType }
    });

    if (existingReportSetting) {
      throw new Error('Report setting with this type already exists');
    }

    // Validate dates
    const openingDate = new Date(sanitizedDto.reportOpeningDate);
    const closingDate = new Date(sanitizedDto.reportClosingDate);

    if (closingDate < openingDate) {
      throw new Error('Closing date must be on or after opening date');
    }

    // Create domain entity
    const reportSetting = new ReportSettings(
      crypto.randomUUID(),
      sanitizedDto.reportType,
      openingDate,
      closingDate,
      true,
      createdBy,
      new Date(),
      new Date()
    );

    // Save to database
    const prismaData = {
      ReportSettingsId: reportSetting.reportSettingsId,
      ReportType: reportSetting.reportType,
      ReportOpeningDate: reportSetting.reportOpeningDate,
      ReportClosingDate: reportSetting.reportClosingDate,
      IsActive: reportSetting.isActive,
      CreatedBy: reportSetting.createdBy,
      CreatedAt: reportSetting.createdAt,
      UpdatedAt: reportSetting.updatedAt
    };

    const createdReportSetting = await this.prisma.reportSettings.create({
      data: prismaData
    });

    return toReportSettingsDto(createdReportSetting);
  }

  async updateReportSettingAsync(id: string, updateDto: UpdateReportSettingsDto, updatedBy: string): Promise<ReportSettingsDto> {
    // Validate DTO
    const validatedDto = DtoValidator.validate(updateDto, UpdateReportSettingsSchema);

    // Sanitize input
    const sanitizedDto = DtoValidator.sanitizeUserInput(validatedDto);

    // Check if report setting exists
    const existingReportSetting = await this.prisma.reportSettings.findUnique({
      where: { ReportSettingsId: id }
    });

    if (!existingReportSetting) {
      throw new Error('Report setting not found');
    }

    // Check if report type already exists (if being updated)
    if (sanitizedDto.reportType && sanitizedDto.reportType !== existingReportSetting.ReportType) {
      const duplicateReportSetting = await this.prisma.reportSettings.findFirst({
        where: { 
          ReportType: sanitizedDto.reportType,
          ReportSettingsId: { not: id }
        }
      });

      if (duplicateReportSetting) {
        throw new Error('Report setting with this type already exists');
      }
    }

    // Validate dates if both are provided
    if (sanitizedDto.reportOpeningDate && sanitizedDto.reportClosingDate) {
      const openingDate = new Date(sanitizedDto.reportOpeningDate);
      const closingDate = new Date(sanitizedDto.reportClosingDate);

      if (closingDate < openingDate) {
        throw new Error('Closing date must be on or after opening date');
      }
    }

    // Prepare update data
    const updateData: any = {
      UpdatedAt: new Date()
    };

    if (sanitizedDto.reportType) {
      updateData.ReportType = sanitizedDto.reportType;
    }
    if (sanitizedDto.reportOpeningDate) {
      updateData.ReportOpeningDate = new Date(sanitizedDto.reportOpeningDate);
    }
    if (sanitizedDto.reportClosingDate) {
      updateData.ReportClosingDate = new Date(sanitizedDto.reportClosingDate);
    }
    if (sanitizedDto.isActive !== undefined) {
      updateData.IsActive = sanitizedDto.isActive;
    }

    const updatedReportSetting = await this.prisma.reportSettings.update({
      where: { ReportSettingsId: id },
      data: updateData
    });

    return toReportSettingsDto(updatedReportSetting);
  }

  async deleteReportSettingAsync(id: string): Promise<void> {
    const existingReportSetting = await this.prisma.reportSettings.findUnique({
      where: { ReportSettingsId: id }
    });

    if (!existingReportSetting) {
      throw new Error('Report setting not found');
    }

    await this.prisma.reportSettings.delete({
      where: { ReportSettingsId: id }
    });
  }

  async softDeleteReportSettingAsync(id: string): Promise<void> {
    const existingReportSetting = await this.prisma.reportSettings.findUnique({
      where: { ReportSettingsId: id }
    });

    if (!existingReportSetting) {
      throw new Error('Report setting not found');
    }

    await this.prisma.reportSettings.update({
      where: { ReportSettingsId: id },
      data: { 
        IsActive: false,
        UpdatedAt: new Date()
      }
    });
  }

  async getCurrentlyOpenReportSettingsAsync(): Promise<ReportSettingsDto[]> {
    const now = new Date();
    const prismaReportSettings = await this.prisma.reportSettings.findMany({
      where: {
        IsActive: true,
        ReportOpeningDate: { lte: now },
        ReportClosingDate: { gte: now }
      },
      orderBy: { CreatedAt: 'desc' }
    });

    return prismaReportSettings.map(toReportSettingsDto);
  }

  async getUpcomingReportSettingsAsync(daysAhead: number = 7): Promise<ReportSettingsDto[]> {
    const now = new Date();
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + daysAhead);

    const prismaReportSettings = await this.prisma.reportSettings.findMany({
      where: {
        IsActive: true,
        ReportOpeningDate: { 
          gte: now,
          lte: futureDate 
        }
      },
      orderBy: { ReportOpeningDate: 'asc' }
    });

    return prismaReportSettings.map(toReportSettingsDto);
  }
}
